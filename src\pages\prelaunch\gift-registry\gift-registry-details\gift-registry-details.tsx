/* eslint-disable @typescript-eslint/no-explicit-any */
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { PageTitle } from "../../../../components/helmet/helmet";
import { useState, useEffect, useCallback, useRef } from "react";
import {
  Tag2,
  Gift,
  Moneys,
  // ArrowLeft,
  // EyeSlash,
  Box,
  BuyCrypto,
  AddCircle,
  EyeSlash,
  Edit2,
  CloseCircle,
  Camera,
} from "iconsax-react";
import gift from "../../../../assets/images/gift-res.png";
import empty from "../../../../assets/images/empy.png";
import { Icon } from "../../../../components/icons/icon";
// import iphone from '../../../../assets/images/iphone.svg';

// import { CreateGiftRegistry } from '../create-gift-registry';
import {
  useInfiniteQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import { GiftRegistryServices } from "../../../../lib/services/gift-registry";
import { useEventStore } from "../../../../lib/store/event";
import { MoreVertical } from "lucide-react";
import { toast } from "react-toastify";
import { useToolStatusRefresh } from "../../../../lib/hooks/useToolStatusRefresh";
import { useUserAuthStore } from "../../../../lib/store/auth";
import { GiftRegistry } from "../gift-registry/gift-registry";
import { useCompleteEventData } from "../../../../lib/hooks/useCompleteEventData";
import { AuthServices } from "../../../../lib/services/auth";

export const GiftRegistryDetails = () => {
  const { selectedEvent } = useEventStore();
  const { id } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { refreshToolStatus } = useToolStatusRefresh();
  const { toolStatus } = useUserAuthStore();
  const [activeTab, setActiveTab] = useState("gifts");
  // const [isGiftRegistryModalOpen, setIsGiftRegistryModalOpen] = useState(false);
  const [openPopoverId, setOpenPopoverId] = useState<string | null>(null);
  const [editModal, setEditModal] = useState<{
    type: "gift" | "cash";
    item: any;
  } | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [formData, setFormData] = useState<any>({});
  const [openDropdowns, setOpenDropdowns] = useState<Record<string, boolean>>(
    {}
  );
  const [copied, setCopied] = useState(false);
  const [reservationStatus, setReservationStatus] = useState<
    Record<string, boolean>
  >({});
  const [loadingReservations, setLoadingReservations] = useState<
    Record<string, boolean>
  >({});
  const [hoveredImage, setHoveredImage] = useState<string | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const imageInputRef = useRef<HTMLInputElement>(null);

  const dropdownRef = useRef(null);
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (!(e.target as HTMLElement).closest(".popover-menu"))
        setOpenPopoverId(null);
    };
    if (openPopoverId) document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [openPopoverId]);

  const {
    data: giftItemData,
    isLoading: isGiftLoading,
    error: giftError,
    fetchNextPage: fetchNextGiftPage,
    hasNextPage: hasNextGiftPage,
    isFetchingNextPage: isFetchingNextGiftPage,
    refetch,
  } = useInfiniteQuery({
    queryKey: ["gift-items", selectedEvent?.id],
    queryFn: async ({ pageParam = 1 }) => {
      if (!selectedEvent?.id) throw new Error("No event selected");
      return GiftRegistryServices.getGiftItems(selectedEvent.id, {
        page: pageParam,
        per_page: 5,
      });
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage?.data?.meta) return undefined;
      const meta = lastPage.data.meta;
      return meta.next_page ? meta.page + 1 : undefined;
    },
    enabled: !!selectedEvent?.id,
  });

  const {
    data: cashGiftData,
    isLoading: isCashLoading,
    error: cashError,
    fetchNextPage: fetchNextCashPage,
    hasNextPage: hasNextCashPage,
    isFetchingNextPage: isFetchingNextCashPage,
  } = useInfiniteQuery({
    queryKey: ["cash-gifts", selectedEvent?.id],
    queryFn: async ({ pageParam = 1 }) => {
      if (!selectedEvent?.id) throw new Error("No event selected");
      return GiftRegistryServices.getCashGifts(selectedEvent.id, {
        page: pageParam,
        per_page: 5,
      });
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage?.data?.meta) return undefined;
      const meta = lastPage.data.meta;
      return meta.next_page ? meta.page + 1 : undefined;
    },
    enabled: !!selectedEvent?.id,
  });

  const updateCashGiftMutation = useMutation({
    mutationFn: ({
      giftId,
      payload,
    }: {
      giftId: string;
      payload: {
        amount?: string;
        description?: string;
        is_crowd_gift?: boolean;
        status?: string;
      };
    }) => GiftRegistryServices.updateCashGift(giftId, payload),
    onSuccess: () => {
      toast.success("Cash gift updated successfully!");
      queryClient.invalidateQueries({
        queryKey: ["cash-gifts", selectedEvent?.id],
      });
      refreshToolStatus();
      setEditModal(null);
      setIsUpdating(false);
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to update cash gift"
      );
      setIsUpdating(false);
    },
  });

  const updateItemGiftMutation = useMutation({
    mutationFn: ({
      giftId,
      payload,
    }: {
      giftId: string;
      payload: {
        name?: string;
        description?: string;
        quantity?: number;
        price?: string;
        item_link?: string;
        status?: string;
      };
    }) => GiftRegistryServices.updateItemGift(giftId, payload),
    onSuccess: () => {
      toast.success("Gift item updated successfully!");
      queryClient.invalidateQueries({
        queryKey: ["gift-items", selectedEvent?.id],
      });
      refreshToolStatus();
      setEditModal(null);
      setIsUpdating(false);
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to update gift item"
      );
      setIsUpdating(false);
    },
  });

  const updateGiftVisibilityMutation = useMutation({
    mutationFn: ({
      giftId,
      payload,
      type,
    }: {
      giftId: string;
      payload: { status: string };
      type: "gift" | "cash";
    }) => {
      if (type === "cash") {
        return GiftRegistryServices.updateCashGift(giftId, payload);
      } else {
        return GiftRegistryServices.updateItemGift(giftId, payload);
      }
    },
    onSuccess: (_, variables) => {
      if (variables.type === "cash") {
        queryClient.invalidateQueries({
          queryKey: ["cash-gifts", selectedEvent?.id],
        });
      } else {
        refetch();
      }
      refreshToolStatus();
      setOpenDropdowns({});
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to update item status"
      );
    },
  });

  const handleTabScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
      if (scrollHeight - scrollTop <= clientHeight + 5) {
        if (
          activeTab === "gifts" &&
          hasNextGiftPage &&
          !isFetchingNextGiftPage
        ) {
          fetchNextGiftPage();
        } else if (
          activeTab === "cash" &&
          hasNextCashPage &&
          !isFetchingNextCashPage
        ) {
          fetchNextCashPage();
        }
      }
    },
    [
      activeTab,
      hasNextGiftPage,
      hasNextCashPage,
      isFetchingNextGiftPage,
      isFetchingNextCashPage,
      fetchNextGiftPage,
      fetchNextCashPage,
    ]
  );

  const allGiftItems =
    giftItemData?.pages?.flatMap((page) => page?.data?.gifts || []) || [];

  const allCashItems =
    cashGiftData?.pages?.flatMap((page) => page?.data?.gifts || []) || [];

  useEffect(() => {
    if (editModal) {
      const item = editModal.item;
      const formattedItem = {
        ...item,
        amount: item.amount
          ? formatNumberWithCommas(item.amount.toString())
          : "",
        price: item.price ? formatNumberWithCommas(item.price.toString()) : "",
        quantity: item.quantity || 1,
      };
      setFormData(formattedItem);
    }
  }, [editModal]);

  const handleFormChange = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  const formatNumberWithCommas = (value: string) => {
    if (!value) return "";
    return value.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const handleAmountChange = (field: string, inputValue: string) => {
    const value = inputValue.replace(/[^0-9,]/g, "");
    const numericValue = value.replace(/,/g, "");
    const formattedValue = formatNumberWithCommas(numericValue);
    handleFormChange(field, formattedValue);
  };

  const validateForm = () => {
    if (!editModal) return false;

    if (editModal.type === "cash") {
      const numericAmount =
        formData?.amount?.toString().replace(/,/g, "") || "";
      if (!numericAmount) {
        toast.error("Please fill in all required fields");
        return false;
      }
    } else {
      const numericPrice = formData?.price?.toString().replace(/,/g, "") || "";
      if (!formData.name || !numericPrice) {
        toast.error("Please fill in all required fields");
        return false;
      }
    }
    return true;
  };

  const handleSaveChanges = async () => {
    if (!editModal || !validateForm()) return;

    setIsUpdating(true);

    try {
      if (editModal.type === "cash") {
        const payload = {
          amount: formData.amount?.toString().replace(/,/g, ""),
          description: formData.description,
          is_crowd_gift: formData.is_crowd_gift || false,
          status: formData.status || "active",
        };

        Object.keys(payload).forEach((key) => {
          if (payload[key as keyof typeof payload] === undefined) {
            delete payload[key as keyof typeof payload];
          }
        });

        await updateCashGiftMutation.mutateAsync({
          giftId: editModal.item.id,
          payload,
        });
      } else {
        const payload = {
          name: formData.name,
          description: formData.description,
          quantity: formData.quantity,
          price: formData.price?.toString().replace(/,/g, ""),
          item_link: formData.item_link,
          status: formData.status || "active",
        };

        Object.keys(payload).forEach((key) => {
          if (payload[key as keyof typeof payload] === undefined) {
            delete payload[key as keyof typeof payload];
          }
        });

        await updateItemGiftMutation.mutateAsync({
          giftId: editModal.item.id,
          payload,
        });
      }
    } catch (error) {
      console.error("Error updating gift:", error);
    }
  };

  const registry = {
    reservedItems: 0,
    receivedCash: 0,
    amountReceived: 0,
    items: allGiftItems.map((item: any) => ({
      id: item.id,
      name: item.name,
      price: parseFloat(item.price) || 0,
      status: item.status,
      image: item.image_preview_url,
      description: item.description || "",
      reserved: item.reserved_count || 0,
      purchased: item.purchased_count || 0,
      item_link: item.item_link || "",
      quantity: item.quantity || 1,
      total_reservations: item.total_reservations, // Preserve reservation data
    })),
    cashItems: allCashItems.map((item: any) => ({
      id: item.id,
      amount: parseFloat(item.amount) || 0,
      description: item.description,
      received: item.received_count || 0,
      status: item.status || "active",
      is_crowd_gift: item.is_crowd_gift,
      metrics: item.metrics, // Preserve metrics data including is_reserved
    })),
  };
  const handleDropdownToggle = async (
    e: React.MouseEvent,
    itemId: string,
    type: "gift" | "cash"
  ) => {
    e.stopPropagation();

    // Check reservation status when dropdown opens
    if (!openDropdowns[itemId]) {
      await checkReservationStatus(itemId, type);
    }

    setOpenDropdowns((prev) => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  const handleHideFromList = (
    e: React.MouseEvent,
    itemId: string,
    type: "gift" | "cash" = "gift"
  ) => {
    e.stopPropagation();
    updateGiftVisibilityMutation.mutate({
      giftId: itemId,
      payload: {
        status: "disabled",
      },
      type,
    });
    setTimeout(() => {
      setOpenDropdowns((prev) => ({
        ...prev,
        [itemId]: false,
      }));
    }, 0);
  };

  const handleShowFromList = (
    e: React.MouseEvent,
    itemId: string,
    type: "gift" | "cash" = "gift"
  ) => {
    e.stopPropagation();
    updateGiftVisibilityMutation.mutate({
      giftId: itemId,
      payload: {
        status: "active",
      },
      type,
    });
    setTimeout(() => {
      setOpenDropdowns((prev) => ({
        ...prev,
        [itemId]: false,
      }));
    }, 0);
  };

  // Function to check if a gift is reserved
  const checkReservationStatus = async (
    giftId: string,
    type: "gift" | "cash"
  ) => {
    if (loadingReservations[giftId]) return; // Prevent multiple calls

    setLoadingReservations((prev) => ({ ...prev, [giftId]: true }));

    try {
      let response;
      if (type === "cash") {
        response = await GiftRegistryServices.getSingleCashGift(giftId);
        const isReserved = response?.data?.metrics?.is_reserved || false;
        setReservationStatus((prev) => ({ ...prev, [giftId]: isReserved }));
      } else {
        response = await GiftRegistryServices.getSingleGiftItem(giftId);
        const totalReservations =
          response?.data?.metrics?.total_reservations || 0;
        const isReserved = totalReservations > 0;
        setReservationStatus((prev) => ({ ...prev, [giftId]: isReserved }));
      }
    } catch (error) {
      console.error("Failed to check reservation status:", error);
      // Default to not reserved if API fails
      setReservationStatus((prev) => ({ ...prev, [giftId]: false }));
    } finally {
      setLoadingReservations((prev) => ({ ...prev, [giftId]: false }));
    }
  };

  // Function to handle image upload
  const handleImageUpload = async (file: File, giftId: string) => {
    if (!file || !file.type.startsWith("image/")) {
      toast.error("Please select a valid image file");
      return;
    }

    const MAX_FILE_SIZE_MB = 10;
    const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

    if (file.size > MAX_FILE_SIZE_BYTES) {
      toast.error(`Image size must be less than ${MAX_FILE_SIZE_MB}MB`);
      return;
    }

    if (!selectedEvent?.id) {
      toast.error("Event ID not found");
      return;
    }

    setUploadingImage(true);
    try {
      // Upload the image using AuthServices (following PreviewAndCreate.tsx pattern)
      const response = await AuthServices.uploadFiles(
        file,
        "item_gift",
        selectedEvent.id,
        giftId
      );

      console.log("Image upload successful", {
        response: response.data,
        status: response.status || "unknown",
      });

      if (response?.data) {
        toast.success("Image updated successfully!");

        // Refresh the gift items to show the new image
        refetch();

        // Update the edit modal if it's open
        if (editModal && editModal.item.id === giftId) {
          const updatedResponse = await GiftRegistryServices.getSingleGiftItem(
            giftId
          );
          if (updatedResponse?.data) {
            console.log(
              "Updating modal with new image:",
              updatedResponse.data.image_preview_url
            );
            const newImageUrl =
              updatedResponse.data.image_preview_url + "?t=" + Date.now(); // Add timestamp to force refresh
            setEditModal((prev) =>
              prev
                ? {
                    ...prev,
                    item: {
                      ...prev.item,
                      image: newImageUrl,
                      image_preview_url: newImageUrl,
                    },
                  }
                : null
            );
          }
        }
      }
    } catch (error: any) {
      console.error("Image upload failed:", error);
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to upload image";
      toast.error(errorMessage);
    } finally {
      setUploadingImage(false);
    }
  };

  const handleEditGiftItem = async (
    e: React.MouseEvent,
    item: any,
    type: "gift" | "cash"
  ) => {
    e.stopPropagation();

    // Check if we already have reservation data from the list
    if (type === "gift" && item.total_reservations !== undefined) {
      const isReserved = (item.total_reservations || 0) > 0;
      setReservationStatus((prev) => ({ ...prev, [item.id]: isReserved }));
    } else if (type === "cash" && item.metrics?.is_reserved !== undefined) {
      setReservationStatus((prev) => ({
        ...prev,
        [item.id]: item.metrics.is_reserved,
      }));
    } else {
      // If no existing data, fetch fresh data
      // For cash gifts, the list API doesn't include metrics, so we always need to fetch individual details
      await checkReservationStatus(item.id, type);
    }

    // Open modal
    setEditModal({ type, item });

    setTimeout(() => {
      setOpenDropdowns((prev) => ({
        ...prev,
        [item.id]: false,
      }));
    }, 0);
  };
  const copyToClipboard = () => {
    navigator.clipboard
      .writeText(shareableLink)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      })
      .catch((err) => {
        console.error("Failed to copy: ", err);
      });
  };
  const {
    completeEventData,
    isLoading: isLoadingCompleteData,
    error: completeDataError,
  } = useCompleteEventData(selectedEvent?.id);
  const completedCashReservations =
    completeEventData?.gift_metrics?.total_completed_cash_gift_reservations ??
    0;
  const completedItemReservations =
    completeEventData?.gift_metrics?.total_completed_item_gift_reservations ??
    0;
  const totalCompletedReservations =
    completedCashReservations + completedItemReservations;

  const shareableLink = completeEventData?.gift_registry_link || "";
  const formatAmount = (amountString: any) => {
    const num = parseFloat(amountString);
    return isNaN(num) ? "0.00" : num.toFixed(2);
  };

  const formatAmountShort = (amountString: any) => {
    const num = parseFloat(amountString);
    if (isNaN(num)) return "₦0";

    if (num >= 1000000000) {
      return `₦${(num / 1000000000).toFixed(1)}B`;
    } else if (num >= 1000000) {
      return `₦${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `₦${(num / 1000).toFixed(1)}k`;
    } else {
      return `₦${num.toFixed(0)}`;
    }
  };

  const guestStats = [
    {
      label: "GIFT ITEMS",
      count: registry?.items?.length || 0,
      icon: <Icon name="giftItems" />,
    },
    {
      label: "RESERVED ITEMS",
      count: totalCompletedReservations || 0,
      icon: <Icon name="reserved" />,
    },
    {
      label: "CASH GIFTS",
      count: registry?.cashItems?.length || 0,
      icon: <Icon name="cashGift" color="#F5F4FF" secondaryColor="#7F7AB2" />,
    },
    {
      label: "RECEIVED CASH",
      count: formatAmountShort(
        completeEventData?.gift_metrics
          ?.total_completed_cash_gift_reservations_amount
      ),
      icon: <Icon name="markedCircle" />,
    },
  ];

  if (isGiftLoading || isCashLoading || isLoadingCompleteData)
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );

  if (giftError || cashError || completeDataError)
    return (
      <div className="flex justify-center items-center h-screen">
        <p>Sorry, An error occured. Kindly, Refresh</p>
      </div>
    );

  console.log("registry.items", registry.items);
  return (
    <div className="bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] pt-32 ">
      <PageTitle
        title="Gift Registry"
        description="View gift registry details"
      />
      {toolStatus?.has_gift_registry ? (
        <>
          <div className="max-w-[564px] mx-auto px-4 md:px-0 pb-20 ">
            {/* <div className="pt-8 mb-6">
          <button
            onClick={() => navigate(-1)}
            className="p-2.5 bg-white rounded-full cursor-pointer">
            <div className="bg-[#F5F6FE] w-fit p-0.5">
              <div className="bg-primary p-0.5 rounded-full">
                <ArrowLeft size="16" color="#fff" />
              </div>
            </div>
          </button>
        </div> */}
            <div className="flex justify-between mb-6 bg-white pt-6 pl-5 rounded-2xl shadow-[0px_12px_120px_0px_#5F5F5F0F]">
              <div className="max-w-md ">
                <h1 className="text-[28px] font-semibold mb-8">
                  {selectedEvent?.gift_registry_title ||
                    "No Title For Registry"}
                </h1>
                {/* <p className="text-sm text-grey-950 mb-7">
                  {"No description for registry"}
                </p> */}

                <div className=" flex gap-4 mb-5">
                  <div className="flex items-center gap-1 bg-primary-250 pl-2.5 pr-2 py-0.5 rounded-2xl">
                    <Gift variant="Bulk" size={12} color="#4D55F2" />
                    <span className="text-xs italic font-medium text-primary">
                      {registry?.items?.length || 0} Gift Items
                    </span>
                  </div>
                  <div className="flex items-center gap-1 bg-cus-pink-500 pl-2.5 pr-2 rounded-2xl py-0.5">
                    <Moneys variant="Bulk" size={12} color="#FF885E" />
                    <span className="text-cus-orange-250 text-xs italic font-medium">
                      {registry?.cashItems?.length || 0} Cash gifts{" "}
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between bg-[#F5F9FF] max-w-[300px] rounded-full p-1 pl-4 italic">
                  <span className="text-sm text-[#000059]  truncate mr-2">
                    <strong>Gift Registry Link:</strong> {shareableLink}
                  </span>
                  <button
                    type="button"
                    onClick={copyToClipboard}
                    className="bg-[#E1ECFE] text-primary flex items-center gap-1 py-1.5 px-4 rounded-full"
                  >
                    <span className="text-sm font-medium">
                      {copied ? "Copied!" : "Copy"}
                    </span>
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 12 12"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 6.45V8.55C8 10.3 7.3 11 5.55 11H3.45C1.7 11 1 10.3 1 8.55V6.45C1 4.7 1.7 4 3.45 4H5.55C7.3 4 8 4.7 8 6.45Z"
                        fill="#4D55F2"
                      />
                      <path
                        opacity="0.4"
                        d="M8.55281 1H6.45281C4.72781 1 4.02781 1.685 4.00781 3.375H5.55281C7.65281 3.375 8.62781 4.35 8.62781 6.45V7.995C10.3178 7.975 11.0028 7.275 11.0028 5.55V3.45C11.0028 1.7 10.3028 1 8.55281 1Z"
                        fill="#4D55F2"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <img
                src={gift}
                alt="invite-card"
                className="hidden md:block rounded-br-2xl "
              />
            </div>

            <div className="bg-white rounded-2xl mb-2.5 shadow-[0px_12px_120px_0px_#5F5F5F0F]">
              <div className="grid grid-cols-1 md:grid-cols-4 divide-y md:divide-x divide-grey-850">
                {guestStats.map((stat, index) => (
                  <div key={index} className=" pr-3 pl-4 pt-3 pb-5">
                    <div className="flex justify-end mb-3">{stat.icon}</div>
                    <div className="text-[32px] italic font-bold mb-1.5">
                      {stat.count}
                    </div>
                    <div className="text-grey-250 text-xs font-medium uppercase tracking-[0.10em]">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-white flex justify-between py-6 px-4 rounded-xl shadow-[0px_12px_120px_0px_#5F5F5F0F] mb-8">
              <div>
                <p className="text-3xl font-bold italic tracking-[0.04em]">
                  ₦
                  {formatAmount(
                    completeEventData?.gift_metrics
                      ?.total_completed_cash_gift_reservations_amount
                  )}
                </p>
                <p className="text-xs font-medium text-grey-250 uppercase tracking-[0.10em]">
                  Amount Received
                </p>
              </div>
              <button
                className="md:px-4 px-2 py-1 md:py-2 bg-primary rounded-full text-[10px] h-[40px] md:h-auto sm:text-sm font-medium text-white min-w-max w-fit flex items-center gap-2 cursor-pointer "
                onClick={() => navigate("/withdrawal/select-account")}
              >
                Withdraw Balance{" "}
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    opacity="0.4"
                    d="M9.9974 18.3337C14.5998 18.3337 18.3307 14.6027 18.3307 10.0003C18.3307 5.39795 14.5998 1.66699 9.9974 1.66699C5.39502 1.66699 1.66406 5.39795 1.66406 10.0003C1.66406 14.6027 5.39502 18.3337 9.9974 18.3337Z"
                    fill="white"
                  />
                  <path
                    d="M13.3609 9.5582L10.8609 7.0582C10.6193 6.81654 10.2193 6.81654 9.9776 7.0582C9.73594 7.29987 9.73594 7.69987 9.9776 7.94154L11.4109 9.37487H7.08594C6.74427 9.37487 6.46094 9.6582 6.46094 9.99987C6.46094 10.3415 6.74427 10.6249 7.08594 10.6249H11.4109L9.9776 12.0582C9.73594 12.2999 9.73594 12.6999 9.9776 12.9415C10.1026 13.0665 10.2609 13.1249 10.4193 13.1249C10.5776 13.1249 10.7359 13.0665 10.8609 12.9415L13.3609 10.4415C13.6026 10.1999 13.6026 9.79987 13.3609 9.5582Z"
                    fill="white"
                  />
                </svg>
              </button>
            </div>

            <div className="mb-6 flex justify-between items-center">
              <div className="rounded-full p-1 inline-flex">
                <button
                  className={`px-4 py-2 rounded-full text-sm font-medium cursor-pointer ${
                    activeTab === "gifts"
                      ? "bg-primary text-white"
                      : "text-gray-700"
                  }`}
                  onClick={() => setActiveTab("gifts")}
                >
                  Gift Items • {registry?.items?.length}
                </button>
                <button
                  className={`px-4 py-2 rounded-full text-sm font-medium cursor-pointer ${
                    activeTab === "cash"
                      ? "bg-primary text-white"
                      : "text-gray-700"
                  }`}
                  onClick={() => setActiveTab("cash")}
                >
                  Cash Gifts • {registry?.cashItems?.length || 0}
                </button>
              </div>
              <button
                type="button"
                onClick={() => navigate("/create-gift-registry")}
                // onClick={() => setIsGiftRegistryModalOpen(true)}
                className="flex items-center gap-1 text-primary font-medium text-xs rounded-full bg-primary-150 px-2 py-1.5"
              >
                <AddCircle size="24" color="#4D55F2" variant="Bulk" />
                <span> Add New</span>
              </button>
            </div>

            {activeTab === "gifts" &&
              (registry.items.length > 0 ? (
                <div
                  className="space-y-4 max-h-[900px] overflow-y-auto pr-2 scrollbar-thin [&::-webkit-scrollbar]:hidden scrollbar-thumb-gray-300 scrollbar-track-gray-100"
                  onScroll={handleTabScroll}
                >
                  {registry.items
                    .sort((a, b) => {
                      if (a.status === "disabled" && b.status !== "disabled")
                        return 1;
                      if (a.status !== "disabled" && b.status === "disabled")
                        return -1;
                      return 0;
                    })
                    .map((item: any) => (
                      <div
                        key={item.id}
                        className="bg-white relative rounded-xl flex flex-col md:flex-row items-center gap-4.5 cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() =>
                          navigate(`/gift-registry/${id}/gift/${item.id}`)
                        }
                      >
                        {item.status === "disabled" && (
                          <div className="absolute inset-0 bg-black/30 rounded-xl flex items-center justify-center z-30">
                            <button
                              onClick={(e) => handleShowFromList(e, item.id)}
                              className="bg-white px-4 py-2 rounded-full shadow-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                            >
                              Show Item on List
                            </button>
                          </div>
                        )}

                        <div
                          className="absolute top-4 right-4 z-10"
                          ref={dropdownRef}
                        >
                          <button
                            onClick={(e) =>
                              handleDropdownToggle(e, item.id, "gift")
                            }
                            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                          >
                            <MoreVertical size={20} color="#999999" />
                          </button>

                          {openDropdowns[item.id] && (
                            <div
                              data-dropdown-menu
                              className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-2 min-w-[160px] z-20"
                            >
                              {item.status === "disabled" ? (
                                <button
                                  onClick={(e) =>
                                    handleShowFromList(e, item.id)
                                  }
                                  disabled={
                                    updateGiftVisibilityMutation.isPending
                                  }
                                  className="w-full px-4 py-2 text-left text-sm text-grey-950 flex items-center gap-2"
                                >
                                  Show on List
                                </button>
                              ) : (
                                <button
                                  onClick={(e) =>
                                    handleHideFromList(e, item.id)
                                  }
                                  disabled={
                                    updateGiftVisibilityMutation.isPending
                                  }
                                  className="w-full px-4 py-2 text-left text-sm text-grey-950 flex items-center gap-2"
                                >
                                  <EyeSlash
                                    size={16}
                                    color="#5F66F3"
                                    variant="Bulk"
                                  />
                                  Hide from List
                                </button>
                              )}
                              <button
                                onClick={(e) =>
                                  handleEditGiftItem(e, item, "gift")
                                }
                                disabled={
                                  loadingReservations[item.id] ||
                                  reservationStatus[item.id]
                                }
                                className={`w-full px-4 py-2 text-left text-sm flex items-center gap-2 ${
                                  loadingReservations[item.id] ||
                                  reservationStatus[item.id]
                                    ? "text-gray-400 cursor-not-allowed"
                                    : "text-grey-950 hover:bg-gray-50"
                                }`}
                              >
                                {loadingReservations[item.id] ? (
                                  <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
                                ) : (
                                  <Edit2
                                    size={16}
                                    color={
                                      reservationStatus[item.id]
                                        ? "#9CA3AF"
                                        : "#5F66F3"
                                    }
                                    variant="Bulk"
                                  />
                                )}
                                {reservationStatus[item.id]
                                  ? "Item Reserved"
                                  : "Edit Gift Item"}
                              </button>
                            </div>
                          )}
                        </div>

                        <div
                          className="w-full rounded-t-xl md:rounded-[unset] md:w-[155px] h-[184px] bg-gray-200 md:rounded-l-lg overflow-hidden relative group"
                          onMouseEnter={() => setHoveredImage(item.id)}
                          onMouseLeave={() => setHoveredImage(null)}
                        >
                          <img
                            src={item.image}
                            alt={item.name}
                            className="w-full h-full object-cover"
                          />
                          {/* Image Edit Overlay - Only show if not reserved */}
                          {hoveredImage === item.id &&
                            item.image &&
                            !reservationStatus[item.id] && (
                              <div className="absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity duration-200">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    imageInputRef.current?.click();
                                  }}
                                  disabled={uploadingImage}
                                  className="bg-white/90 hover:bg-white text-gray-800 rounded-full p-3 transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
                                  title="Change Image"
                                >
                                  {uploadingImage ? (
                                    <div className="w-5 h-5 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
                                  ) : (
                                    <Camera color="black" size={20} />
                                  )}
                                </button>
                                <input
                                  ref={imageInputRef}
                                  type="file"
                                  accept="image/*"
                                  onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) {
                                      handleImageUpload(file, item.id);
                                    }
                                  }}
                                  className="hidden"
                                />
                              </div>
                            )}
                        </div>

                        <div className="flex-1 text-center md:text-left p-4">
                          <h3 className="text-[22px] truncate max-w-[300px] font-medium text-grey-750">
                            {item.name}
                          </h3>
                          <p className="text-base text-grey-100 max-w-[300px] truncate">
                            {item.description}
                          </p>
                          <div className="mt-5.5 mb-4 flex flex-col md:flex-row gap-2 items-center ">
                            <div className="flex items-center gap-2  bg-light-blue-150 text-orange-700 px-2.5 py-1.5 rounded-full">
                              <Tag2 size={12} variant="Bulk" color="#5925DC" />
                              <span className="text-primary text-sm font-semibold">
                                ₦{item.price.toLocaleString()}
                              </span>
                            </div>
                            {item.quantity > 1 && (
                              <p className="bg-[#F5F5F5] text-[#414651] font-semibold px-2.5 py-1.5 rounded-full text-sm">
                                {" "}
                                • {item.quantity || 0} Qty
                              </p>
                            )}

                            {item.reserved > 0 && (
                              <div className="flex items-center gap-1 font-bold italic bg-orange-100 text-orange-700 text-sm px-2.5 py-1.5 rounded-full">
                                <Box size={12} variant="Bulk" color="#C4320A" />
                                <span> Reserved • {item.reserved}</span>
                              </div>
                            )}
                            {item.purchased > 0 && (
                              <span className="flex items-center gap-1 font-bold italic bg-green-100 text-green-700 text-sm px-2.5 py-1.5 rounded-full">
                                <BuyCrypto
                                  size={12}
                                  variant="Bulk"
                                  color="#3CC35C"
                                />
                                Purchased • {item.purchased}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  {isFetchingNextGiftPage && (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-white p-14 rounded-2xl flex flex-col items-center justify-center">
                  <img src={empty} alt="empty-state" />
                  <h3 className="text-[28px] -tracking-[0.04em] font-medium mt-3 mb-2">
                    No Gift Item here currently{" "}
                  </h3>
                  <p className="text-grey-250 text-base mb-5 text-center">
                    You have no Gifts Items currently, <br /> Get started by
                    clicking the button below
                  </p>
                  <button
                    type="button"
                    // onClick={() => setIsGiftRegistryModalOpen(true)}
                    onClick={() => navigate("/create-gift-registry")}
                    className="bg-primary text-white font-medium py-2 px-4 rounded-full mb-25"
                  >
                    Add Gift Items
                  </button>
                </div>
              ))}

            {activeTab === "cash" &&
              (registry.cashItems && registry.cashItems.length > 0 ? (
                <div
                  className="max-h-[600px] [&::-webkit-scrollbar]:hidden overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
                  onScroll={handleTabScroll}
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {registry.cashItems
                      .sort((a, b) => {
                        // Active items (not disabled) come first
                        if (a.status === "disabled" && b.status !== "disabled")
                          return 1;
                        if (a.status !== "disabled" && b.status === "disabled")
                          return -1;
                        return 0; // Keep original order for items with same status
                      })
                      .map((item: any) => (
                        <div
                          key={item.id}
                          className="bg-white rounded-2xl px-4 py-5 relative hover:shadow-lg transition-shadow cursor-pointer"
                          onClick={() =>
                            navigate(`/gift-registry/${id}/cash/${item.id}`)
                          }
                        >
                          {item.status === "disabled" && (
                            <div className="absolute inset-0 bg-black/30 rounded-2xl flex items-center justify-center z-30">
                              <button
                                onClick={(e) =>
                                  handleShowFromList(e, item.id, "cash")
                                }
                                className="bg-white px-4 py-2 rounded-full shadow-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                              >
                                Show Item on List
                              </button>
                            </div>
                          )}

                          <div
                            className="absolute top-3 right-3 z-10"
                            ref={dropdownRef}
                          >
                            <button
                              onClick={(e) =>
                                handleDropdownToggle(e, item.id, "cash")
                              }
                              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                            >
                              <MoreVertical size={20} color="#999999" />
                            </button>

                            {openDropdowns[item.id] && (
                              <div
                                data-dropdown-menu
                                className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-2 min-w-[160px] z-20"
                              >
                                {item.status === "disabled" ? (
                                  <button
                                    onClick={(e) =>
                                      handleShowFromList(e, item.id, "cash")
                                    }
                                    disabled={
                                      updateGiftVisibilityMutation.isPending
                                    }
                                    className="w-full px-4 py-2 text-left text-sm text-grey-950 flex items-center gap-2"
                                  >
                                    Show on List
                                  </button>
                                ) : (
                                  <button
                                    onClick={(e) =>
                                      handleHideFromList(e, item.id, "cash")
                                    }
                                    disabled={
                                      updateGiftVisibilityMutation.isPending
                                    }
                                    className="w-full px-4 py-2 text-left text-sm text-grey-950 flex items-center gap-2"
                                  >
                                    <EyeSlash
                                      size={16}
                                      color="#5F66F3"
                                      variant="Bulk"
                                    />
                                    Hide from List
                                  </button>
                                )}
                                <button
                                  onClick={(e) =>
                                    handleEditGiftItem(e, item, "cash")
                                  }
                                  disabled={
                                    loadingReservations[item.id] ||
                                    reservationStatus[item.id]
                                  }
                                  className={`w-full px-4 py-2 text-left text-sm flex items-center gap-2 ${
                                    loadingReservations[item.id] ||
                                    reservationStatus[item.id]
                                      ? "text-gray-400 cursor-not-allowed"
                                      : "text-grey-950 hover:bg-gray-50"
                                  }`}
                                >
                                  {loadingReservations[item.id] ? (
                                    <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
                                  ) : (
                                    <Edit2
                                      size={16}
                                      color={
                                        reservationStatus[item.id]
                                          ? "#9CA3AF"
                                          : "#5F66F3"
                                      }
                                      variant="Bulk"
                                    />
                                  )}
                                  {reservationStatus[item.id]
                                    ? "Cash Gift Reserved"
                                    : "Edit Cash Gift"}
                                </button>
                              </div>
                            )}
                          </div>

                          <Moneys variant="Bulk" size={56} color="#E2CBC1" />
                          <p className="text-[32px] font-bold ">
                            ₦{item.amount.toLocaleString()}
                          </p>
                          <p className="text-grey-250 text-base italic mb-13.5">
                            {item.description}
                          </p>
                          {item.received > 0 && (
                            <div className="flex items-center gap-1 w-fit font-bold italic bg-grin text-grin-100 text-sm px-2.5 py-1.5 rounded-full">
                              <Box size={12} variant="Bulk" color="#3CC35C" />
                              <span> Reserved • {item.received}</span>
                            </div>
                          )}
                        </div>
                      ))}
                  </div>

                  {isFetchingNextCashPage && (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-white p-14 rounded-2xl flex flex-col items-center justify-center">
                  <img src={empty} alt="empty-state" />
                  <h3 className="text-[28px] -tracking-[0.04em] font-medium mt-3 mb-2">
                    No Cash gifts here currently
                  </h3>
                  <p className="text-grey-250 text-base mb-5 text-center">
                    You have no Cash gifts currently.
                    <br /> Get started by clicking the button below
                  </p>
                  <button
                    type="button"
                    onClick={() => navigate("/create-gift-registry")}
                    // onClick={() => setIsGiftRegistryModalOpen(true)}
                    className="bg-primary text-white font-medium py-2 px-4 rounded-full mb-25"
                  >
                    Add Cash gift
                  </button>
                </div>
              ))}
          </div>
          {editModal && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30 ">
              <div
                className={`bg-white rounded-2xl px-16  p-10 w-full max-w-[95%] max-h-[95vh] md:max-w-[800px] relative shadow-xl flex ${
                  editModal.type === "gift"
                    ? "flex-col md:flex-row overflow-y-auto gap-8 xl:scale-[0.8]"
                    : "flex-col"
                }`}
                style={{ minHeight: editModal.type === "gift" ? 680 : 492 }}
              >
                {/* Close icon */}
                <button
                  className="absolute top-6 right-6 p-0.5 rounded-full hover:bg-gray-100"
                  onClick={() => setEditModal(null)}
                  style={{ width: 32, height: 32 }}
                >
                  <CloseCircle size={32} color="#8288f6" variant="Bulk" />
                </button>
                {/* Header (spans both columns) */}
                {editModal.type === "gift" && (
                  <div className="absolute left-10 top-10 z-10">
                    <h2 className="text-[28px] font-semibold text-black leading-none">
                      {reservationStatus[editModal.item.id]
                        ? "Gift Item Details (Reserved)"
                        : "Edit Gift Item"}
                    </h2>
                    {reservationStatus[editModal.item.id] && (
                      <p className="text-sm text-orange-600 mt-1">
                        This item has been reserved and cannot be edited
                      </p>
                    )}
                  </div>
                )}
                {/* Gift Item Image (left column) */}
                {editModal.type === "gift" && (
                  <div
                    className="flex-shrink-0 w-full md:w-[281px] h-[180px] md:h-[399px] rounded-[18px] bg-gray-100 flex items-center justify-center overflow-hidden mt-[60px] relative group"
                    onMouseEnter={() => setHoveredImage("edit-modal")}
                    onMouseLeave={() => setHoveredImage(null)}
                  >
                    <img
                      src={
                        editModal.item.image ||
                        "https://via.placeholder.com/281x399?text=Gift+Image"
                      }
                      alt="Gift"
                      className="object-cover w-full h-full rounded-[18px]"
                    />
                    {/* Image Edit Overlay for Modal */}
                    {hoveredImage === "edit-modal" && editModal.item.image && (
                      <div className="absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity duration-200 rounded-[18px]">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            imageInputRef.current?.click();
                          }}
                          disabled={uploadingImage}
                          className="bg-white/90 hover:bg-white text-gray-800 rounded-full p-4 transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Change Image"
                        >
                          {uploadingImage ? (
                            <div className="w-6 h-6 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <Camera size={24} />
                          )}
                        </button>
                        <input
                          ref={imageInputRef}
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file && editModal) {
                              handleImageUpload(file, editModal.item.id);
                            }
                          }}
                          className="hidden"
                        />
                      </div>
                    )}
                  </div>
                )}
                {/* Form (right column for gift, full for cash) */}
                <div
                  className={`flex-1 ${
                    editModal.type === "gift"
                      ? "flex flex-col justify-center mt-2"
                      : ""
                  }`}
                >
                  {/* Title for cash only */}
                  {editModal.type === "cash" && (
                    <div className="mb-5">
                      <h2 className="text-[24px] font-semibold text-black text-left leading-none">
                        {reservationStatus[editModal.item.id]
                          ? "Cash Gift Details (Reserved)"
                          : "Edit Cash Gift"}
                      </h2>
                      {reservationStatus[editModal.item.id] && (
                        <p className="text-sm text-orange-600 mt-1">
                          This cash gift has been reserved and cannot be edited
                        </p>
                      )}
                    </div>
                  )}
                  <div className="flex flex-col gap-4 pb-6">
                    {editModal.type === "cash" ? (
                      <>
                        {/* Amount */}
                        <div>
                          <label className="block text-[14px] font-medium text-[#414651] mb-2">
                            Cash gift Amount
                          </label>
                          <div className="flex items-center border border-[#D5D7DA] rounded-[32px] px-4 py-3 bg-white w-full max-w-full">
                            <span className="mr-2 text-[#717680] text-[16px]">
                              ₦
                            </span>
                            <input
                              className="flex-1 outline-none bg-transparent text-[16px] placeholder-[#717680] disabled:opacity-50 disabled:cursor-not-allowed"
                              value={formData.amount || ""}
                              onChange={(e) =>
                                handleAmountChange("amount", e.target.value)
                              }
                              placeholder="Enter Amount"
                              style={{ minWidth: 0 }}
                              disabled={reservationStatus[editModal.item.id]}
                            />
                            <select className="ml-2 text-[#181D27] bg-transparent outline-none text-[16px] font-medium appearance-none pr-6">
                              <option>NGN</option>
                            </select>
                            <svg
                              className="w-4 h-4 ml-[-22px] pointer-events-none"
                              fill="none"
                              stroke="#717680"
                              strokeWidth="2"
                              viewBox="0 0 24 24"
                            >
                              <path d="M6 9l6 6 6-6" />
                            </svg>
                          </div>
                        </div>
                        {/* Reason */}
                        <div>
                          <label className="block text-[14px] font-medium text-[#414651] mb-2">
                            Reason For Cashgift
                          </label>
                          <input
                            className="w-full border border-[#D5D7DA] rounded-[32px] px-4 py-3 text-[16px] h-[44px] placeholder-[#717680] bg-white outline-none disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50"
                            value={formData.description || ""}
                            onChange={(e) =>
                              handleFormChange("description", e.target.value)
                            }
                            placeholder="E.g Trip to Zanzibar"
                            disabled={reservationStatus[editModal.item.id]}
                          />
                        </div>
                        {/* Crowd gifting toggle */}
                        <div className="flex flex-col gap-2 mt-2">
                          <span className="text-[12px] font-medium text-[#125CF2] underline cursor-pointer w-fit">
                            Do you want to enable crowd gifting
                          </span>
                          <div className="flex items-center gap-3 mt-2">
                            <input
                              type="checkbox"
                              className="toggle toggle-primary disabled:opacity-50 disabled:cursor-not-allowed"
                              checked={formData.is_crowd_gift || false}
                              onChange={(e) =>
                                handleFormChange(
                                  "is_crowd_gift",
                                  e.target.checked
                                )
                              }
                              disabled={reservationStatus[editModal.item.id]}
                            />
                            <span className="text-[14px] font-medium text-[#414651]">
                              Enable Crowd gifting
                            </span>
                          </div>
                        </div>
                      </>
                    ) : (
                      <>
                        {/* Gift Name */}
                        <div>
                          <label className="block text-[14px] font-medium text-[#414651] mb-2">
                            Gift Name
                          </label>
                          <input
                            className="w-full border border-[#D5D7DA] rounded-[32px] px-4 py-3 text-[16px] h-[44px] placeholder-[#717680] bg-white outline-none disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50"
                            value={formData.name || ""}
                            onChange={(e) =>
                              handleFormChange("name", e.target.value)
                            }
                            placeholder="Enter Gift Name"
                            disabled={reservationStatus[editModal.item.id]}
                          />
                          {/* Quantity selector */}
                          <div className="my-2 flex items-center justify-end">
                            <div className="flex items-center border border-gray-300 rounded-full">
                              <button
                                className="border-r px-3 py-1 rounded-l-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={
                                  (formData.quantity || 1) === 1 ||
                                  reservationStatus[editModal.item.id]
                                }
                                onClick={() =>
                                  handleFormChange(
                                    "quantity",
                                    Math.max(1, (formData.quantity || 1) - 1)
                                  )
                                }
                              >
                                <svg
                                  width="20"
                                  height="20"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    d="M6 12h12"
                                    stroke="#000"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                  />
                                </svg>
                              </button>
                              <span className="py-1 text-sm font-medium text-gray-900 px-3.5 text-center">
                                {formData.quantity || 1}
                              </span>
                              <button
                                className="border-l rounded-r-lg transition-colors px-3 py-1 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={reservationStatus[editModal.item.id]}
                                onClick={() =>
                                  handleFormChange(
                                    "quantity",
                                    (formData.quantity || 1) + 1
                                  )
                                }
                              >
                                <svg
                                  width="20"
                                  height="20"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    d="M12 6v12M6 12h12"
                                    stroke="#000"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                  />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>

                        {/* Gift Price */}
                        <div>
                          <label className="block text-[14px] font-medium text-[#414651] mb-2">
                            Gift Price
                          </label>
                          <div className="flex items-center border h-[44px] border-[#D5D7DA] rounded-[32px] px-4 py-3 bg-white w-full max-w-full">
                            <span className="mr-2 text-[#717680] text-[16px]">
                              ₦
                            </span>
                            <input
                              className="flex-1 outline-none bg-transparent text-[16px] placeholder-[#717680] disabled:opacity-50 disabled:cursor-not-allowed"
                              value={formData.price || ""}
                              onChange={(e) =>
                                handleAmountChange("price", e.target.value)
                              }
                              placeholder="Enter Price"
                              style={{ minWidth: 0 }}
                              disabled={reservationStatus[editModal.item.id]}
                            />
                            <select className="ml-2 text-[#181D27] bg-transparent outline-none text-[16px] font-medium appearance-none pr-6">
                              <option>NGN</option>
                            </select>
                            <svg
                              className="w-4 h-4 ml-[-22px] pointer-events-none"
                              fill="none"
                              stroke="#717680"
                              strokeWidth="2"
                              viewBox="0 0 24 24"
                            >
                              <path d="M6 9l6 6 6-6" />
                            </svg>
                          </div>
                        </div>
                        {/* Description */}
                        <div>
                          <label className="block text-[14px] font-medium text-[#414651] mb-2">
                            Description
                          </label>
                          <textarea
                            className="w-full border border-[#D5D7DA] rounded-[8px] px-4 py-3 text-[16px] h-[44px] placeholder-[#717680] bg-white outline-none resize-none min-h-[86px] disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50"
                            value={formData.description || ""}
                            onChange={(e) =>
                              handleFormChange("description", e.target.value)
                            }
                            placeholder="Describe your gift item"
                            disabled={reservationStatus[editModal.item.id]}
                          />
                        </div>
                        {/* Link to Item */}
                        <div>
                          <label className="block text-[14px] font-medium text-[#414651] mb-2">
                            Link to Item{" "}
                          </label>
                          <div className="relative">
                            <div className="absolute left-4 top-1/2 -translate-y-1/2 text-[black]">
                              https://
                            </div>
                            <input
                              type="text"
                              value={formData.item_link || ""}
                              onChange={(e) =>
                                handleFormChange("item_link", e.target.value)
                              }
                              placeholder="Paste link to item"
                              className="w-full h-11 pl-[100px] pr-3.5 border border-gray-300 rounded-full text-base font-normal text-[#717680] placeholder:font-normal placeholder:text-grey-700 italic outline-0 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50"
                              disabled={reservationStatus[editModal.item.id]}
                            />
                          </div>
                        </div>
                        {/* Most wanted toggle - Currently disabled as per requirements */}
                        <div className="flex items-center gap-3 mb-4">
                          <button
                            className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                              formData.mostWanted
                                ? "bg-primary"
                                : "bg-primary-650"
                            }`}
                            onClick={() =>
                              handleFormChange(
                                "mostWanted",
                                !formData.mostWanted
                              )
                            }
                            disabled={true} // Disabled for now as per requirements
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-grey-350 shadow-[0px_1px_2px_0px_#0A0D120F,_0px_1px_3px_0px_#0A0D121A] transition-transform ${
                                formData.mostWanted
                                  ? "translate-x-4"
                                  : "translate-x-0.5"
                              }`}
                            />
                          </button>
                          <span className="text-sm font-medium text-grey-500">
                            Set as Most wanted Item
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
                {/* Save Changes button (spans both columns) */}
                <button
                  className={`absolute left-10 bottom-10 w-[calc(100%-80px)] py-4 rounded-[32px] font-bold text-[16px] shadow-sm disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 ${
                    editModal.type === "gift" ? "relative md:absolute" : ""
                  } ${
                    reservationStatus[editModal.item.id]
                      ? "bg-gray-400 cursor-not-allowed"
                      : "bg-[#4D55F2] text-white"
                  }`}
                  onClick={
                    reservationStatus[editModal.item.id]
                      ? () => setEditModal(null)
                      : handleSaveChanges
                  }
                  disabled={isUpdating}
                >
                  {isUpdating && (
                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                  )}
                  {isUpdating
                    ? "Saving..."
                    : reservationStatus[editModal.item.id]
                    ? "Close Details"
                    : "Save Changes"}
                </button>
              </div>
            </div>
          )}
        </>
      ) : (
        <GiftRegistry />
      )}
    </div>
  );
};
